# Test script for SCO shim commands
param(
    [switch]$Verbose = $false
)

$ScoPath = ".\Build\msvc-debug\sco.exe"

function Write-TestHeader {
    param([string]$Title)
    Write-Host "`n" + "="*50 -ForegroundColor Cyan
    Write-Host " $Title" -ForegroundColor Yellow
    Write-Host "="*50 -ForegroundColor Cyan
}

function Test-ShimCommand {
    param([string]$Command, [string]$Expected)
    
    Write-Host "`nTesting: $Command" -ForegroundColor Blue
    try {
        $output = & $ScoPath $Command.Split(' ') 2>&1 | Out-String
        $exitCode = $LASTEXITCODE
        
        if ($exitCode -eq 0) {
            Write-Host "✓ Command succeeded" -ForegroundColor Green
            if ($Verbose) {
                Write-Host "Output:" -ForegroundColor Gray
                Write-Host $output -ForegroundColor Gray
            }
            return $true
        } else {
            Write-Host "✗ Command failed (exit code: $exitCode)" -ForegroundColor Red
            Write-Host "Output:" -ForegroundColor Gray
            Write-Host $output -ForegroundColor Gray
            return $false
        }
    } catch {
        Write-Host "✗ Command failed with exception: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main test execution
Write-Host "SCO Shim Commands Test" -ForegroundColor Cyan

# Check if SCO exists
if (-not (Test-Path $ScoPath)) {
    Write-Host "✗ SCO not found at $ScoPath" -ForegroundColor Red
    exit 1
}

Write-Host "✓ SCO found at $ScoPath" -ForegroundColor Green

$results = @{}

# Test 1: List shims
Write-TestHeader "Test 1: List Shims"
$results["list"] = Test-ShimCommand "shim list"

# Test 2: Add shim
Write-TestHeader "Test 2: Add Shim"
$results["add"] = Test-ShimCommand "shim add test_calc C:\Windows\System32\calc.exe"

# Test 3: List shims again (should show new shim)
Write-TestHeader "Test 3: List Shims (after add)"
$results["list_after_add"] = Test-ShimCommand "shim list"

# Test 4: Info shim
Write-TestHeader "Test 4: Shim Info"
$results["info"] = Test-ShimCommand "shim info test_calc"

# Test 5: Remove shim
Write-TestHeader "Test 5: Remove Shim"
$results["remove"] = Test-ShimCommand "shim remove test_calc"

# Test 6: List shims again (should not show removed shim)
Write-TestHeader "Test 6: List Shims (after remove)"
$results["list_after_remove"] = Test-ShimCommand "shim list"

# Test 7: Test rm alias
Write-TestHeader "Test 7: Test rm Alias"
& $ScoPath shim add test_calc2 C:\Windows\System32\calc.exe | Out-Null
$results["rm_alias"] = Test-ShimCommand "shim rm test_calc2"

# Test 8: Repair shims
Write-TestHeader "Test 8: Repair Shims"
$results["repair"] = Test-ShimCommand "shim repair"

# Test 9: Alter shim (should show not implemented)
Write-TestHeader "Test 9: Alter Shim"
if (& $ScoPath shim list 2>&1 | Select-String "notepad") {
    $results["alter"] = Test-ShimCommand "shim alter notepad"
} else {
    Write-Host "Skipping alter test (no notepad shim found)" -ForegroundColor Yellow
    $results["alter"] = $true
}

# Test 10: Help
Write-TestHeader "Test 10: Help"
$results["help"] = Test-ShimCommand "shim --help"

# Summary
Write-TestHeader "Test Results Summary"

$passCount = ($results.Values | Where-Object { $_ -eq $true }).Count
$failCount = ($results.Values | Where-Object { $_ -eq $false }).Count
$totalCount = $results.Count

foreach ($test in $results.Keys) {
    $result = $results[$test]
    $status = if ($result) { "✓ PASS" } else { "✗ FAIL" }
    $color = if ($result) { "Green" } else { "Red" }
    
    Write-Host "$($test.ToUpper().PadRight(20)) $status" -ForegroundColor $color
}

Write-Host "`nOverall Results:" -ForegroundColor White
Write-Host "  Passed: $passCount/$totalCount" -ForegroundColor Green
Write-Host "  Failed: $failCount/$totalCount" -ForegroundColor $(if ($failCount -eq 0) { "Green" } else { "Red" })

if ($failCount -eq 0) {
    Write-Host "`n🎉 All shim commands working correctly!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n❌ Some shim commands failed" -ForegroundColor Red
    exit 1
}
